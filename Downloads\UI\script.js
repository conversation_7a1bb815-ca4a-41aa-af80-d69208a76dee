// Mobile-optimized JavaScript for patient dashboard

document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.querySelector('.search-input');
    const patientCards = document.querySelectorAll('.patient-card');
    
    searchInput.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        
        patientCards.forEach(card => {
            const patientName = card.querySelector('.patient-name').textContent.toLowerCase();
            const account = card.querySelector('.account').textContent.toLowerCase();
            const room = card.querySelector('.room').textContent.toLowerCase();
            
            if (patientName.includes(searchTerm) || 
                account.includes(searchTerm) || 
                room.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });
    
    // Tab switching functionality
    const myPatientsTab = document.querySelector('.my-patients-tab');
    const hiddenTab = document.querySelector('.hidden-tab');
    
    myPatientsTab.addEventListener('click', function() {
        myPatientsTab.classList.add('active');
        hiddenTab.classList.remove('active');
        // Show all patient cards
        patientCards.forEach(card => {
            card.style.display = 'block';
        });
    });
    
    hiddenTab.addEventListener('click', function() {
        hiddenTab.classList.add('active');
        myPatientsTab.classList.remove('active');
        // Hide all patient cards (simulate hidden patients)
        patientCards.forEach(card => {
            card.style.display = 'none';
        });
    });
    
    // Action button handlers
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add visual feedback for touch
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // Handle different button types
            if (this.classList.contains('delete-btn')) {
                if (confirm('Are you sure you want to remove this patient from your list?')) {
                    this.closest('.patient-card').style.display = 'none';
                }
            } else if (this.classList.contains('play-btn')) {
                alert('Opening patient details...');
            } else if (this.classList.contains('edit-btn')) {
                alert('Edit patient information...');
            } else if (this.classList.contains('profile-btn')) {
                alert('View patient profile...');
            } else if (this.classList.contains('more-btn')) {
                alert('More options...');
            }
        });
    });
    
    // Menu button handler
    document.querySelector('.menu-btn').addEventListener('click', function() {
        alert('Menu options would appear here');
    });
    
    // Filter and sort button handlers
    document.querySelector('.filter-btn').addEventListener('click', function() {
        alert('Filter options would appear here');
    });
    
    document.querySelector('.sort-btn').addEventListener('click', function() {
        alert('Sort options would appear here');
    });
    
    // Facility select handler
    document.querySelector('.facility-select').addEventListener('change', function() {
        console.log('Facility changed to:', this.value);
    });
    
    // Touch optimization for iOS Safari
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
        document.body.style.webkitTouchCallout = 'none';
        document.body.style.webkitUserSelect = 'none';
    }
    
    // Prevent zoom on double tap for better mobile experience
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(event) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
});

// Utility function to handle responsive behavior
function handleResize() {
    const container = document.querySelector('.container');
    const width = window.innerWidth;
    
    if (width < 768) {
        container.classList.add('mobile');
    } else {
        container.classList.remove('mobile');
    }
}

window.addEventListener('resize', handleResize);
handleResize(); // Call on initial load
